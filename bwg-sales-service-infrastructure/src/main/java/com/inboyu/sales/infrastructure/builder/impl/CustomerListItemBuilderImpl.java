package com.inboyu.sales.infrastructure.builder.impl;

import com.inboyu.sales.domain.customer.model.CustomerId;
import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.sales.domain.customer.model.CustomerReservation;
import com.inboyu.sales.domain.customer.model.PlatformCustomerId;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.infrastructure.builder.CustomerListItemBuilder;
import com.inboyu.sales.infrastructure.dao.entity.PlatformCustomerEntity;
import com.inboyu.sales.infrastructure.dao.entity.ReserveSeeHouseEntity;
import com.inboyu.sales.infrastructure.dao.entity.SaleCycleEntity;
import org.springframework.stereotype.Component;

/**
 * 客户列表项构建器实现
 */
@Component
public class CustomerListItemBuilderImpl implements CustomerListItemBuilder {

    @Override
    public CustomerListItem buildCustomerListItem(SaleCycleEntity saleCycle, 
                                                 PlatformCustomerEntity platformCustomer, 
                                                 ReserveSeeHouseEntity reserveSeeHouse) {
        
        // 构建预约记录（如果存在）
        CustomerReservation reservation = null;
        if (reserveSeeHouse != null) {
            reservation = CustomerReservation.builder()
                    .reserveDate(reserveSeeHouse.getReserveDate())
                    .startTime(reserveSeeHouse.getStartTime())
                    .endTime(reserveSeeHouse.getEndTime())
                    .seeState(reserveSeeHouse.getSeeState())
                    .build();
        }

        // 构建客户列表项
        return CustomerListItem.builder()
                .platformCustomerId(PlatformCustomerId.of(platformCustomer.getPlatformCustomerId()))
                .customerId(CustomerId.of(platformCustomer.getCustomerId()))
                .customerName(platformCustomer.getName())
                .storeId(StoreId.of(saleCycle.getStoreId()))
                .saleCycleStartTime(saleCycle.getCreateTime())
                .reservation(reservation)
                .build();
    }
}
