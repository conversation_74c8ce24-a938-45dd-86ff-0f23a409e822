package com.inboyu.sales.infrastructure.builder.impl;

import com.inboyu.sales.domain.behavior.model.BehaviorEvent;
import com.inboyu.sales.domain.behavior.model.BehaviorEventId;
import com.inboyu.sales.domain.behavior.model.BusinessId;
import com.inboyu.sales.domain.behavior.model.EnventType;
import com.inboyu.sales.domain.customer.model.CustomerId;
import com.inboyu.sales.domain.reserve.model.StaffId;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.infrastructure.builder.BehaviorEventBuilder;
import com.inboyu.sales.infrastructure.constant.EventTypeEnum;
import com.inboyu.sales.infrastructure.dao.entity.BehaviorEventEntity;
import com.inboyu.spring.cloud.starter.jpa.utils.NamingUtils;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 行为事件构建器实现
 */
@Component
public class BehaviorEventBuilderImpl implements BehaviorEventBuilder {

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;



    @Override
    public BehaviorEvent toBehaviorEvent(BehaviorEventEntity entity) {
        if (entity == null) {
            return null;
        }

        return BehaviorEvent.builder()
                .behaviorEventId(BehaviorEventId.of(entity.getBehaviorEventId()))
                .eventType(EnventType.create(entity.getEventType(), EventTypeEnum.getByCode(entity.getEventType()).getTitle()))
                .eventContent(entity.getEventContent())
                .businessId(BusinessId.of(entity.getBusinessId()))
                .customerId(CustomerId.of(entity.getCustomerId()))
                .storeId(StoreId.of(entity.getStoreId()))
                .staffId(StaffId.of(entity.getStaffId()))
                .build();
    }

    @Override
    public BehaviorEventEntity toBehaviorEventEntity(BehaviorEvent behaviorEvent) {
        if (behaviorEvent == null) {
            return null;
        }

        BehaviorEventEntity entity = new BehaviorEventEntity();
        entity.setBehaviorEventId(behaviorEvent.getBehaviorEventId().getValue());
        entity.setEventType(behaviorEvent.getEventType().getCode());
        entity.setEventContent(behaviorEvent.getEventContent());
        entity.setBusinessId(behaviorEvent.getBusinessId().getValue());
        entity.setCustomerId(behaviorEvent.getCustomerId().getValue());
        entity.setStoreId(behaviorEvent.getStoreId().getValue());
        entity.setStaffId(behaviorEvent.getStaffId().getValue());
        return entity;
    }

}
