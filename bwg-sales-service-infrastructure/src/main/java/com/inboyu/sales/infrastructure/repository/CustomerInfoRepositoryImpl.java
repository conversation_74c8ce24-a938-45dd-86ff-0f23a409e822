package com.inboyu.sales.infrastructure.repository;

import com.inboyu.admin.api.CustomerFeignClient;
import com.inboyu.admin.dto.response.CustomerResponseDTO;
import com.inboyu.sales.domain.customer.model.CustomerId;
import com.inboyu.sales.domain.customer.model.CustomerInfo;
import com.inboyu.sales.domain.customer.repository.CustomerInfoRepository;
import com.inboyu.spring.cloud.starter.http.api.container.FeignAccessor;
import com.inboyu.spring.cloud.starter.http.api.exception.FeignAppException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 客户信息仓储实现
 */
@Repository
@Slf4j
public class CustomerInfoRepositoryImpl implements CustomerInfoRepository {

    @Autowired
    private FeignAccessor feignAccessor;

    @Override
    public CustomerInfo getCustomerInfo(String customerId) {
        log.info("通过Feign客户端获取客户信息，客户ID：{}", customerId);

        try {

            CustomerFeignClient feignClient = feignAccessor.get(CustomerFeignClient.class);
            CustomerResponseDTO customerResponse = feignClient.getByCustomerId(customerId);
            CustomerResponseDTO customerRealInfoResponse = null;
            try {
                customerRealInfoResponse = feignClient.getCustomerRealInfoByCustomerId(customerId);
            } catch (FeignAppException e) {
                // 客户真实信息不存在是正常的业务场景，使用WARN级别记录
                log.warn("客户真实信息不存在，客户ID：{}，原因：{}", customerId, e.getMessage());
                // 不处理异常，继续执行后续逻辑
            }

            if (customerResponse == null) {
                log.warn("客户信息不存在，客户ID：{}", customerId);
                return null;
            }

            // 将DTO转换为领域模型
            return CustomerInfo.builder()
                    .customerId(CustomerId.of(Long.valueOf(customerId)))
                    .customerName(customerResponse.getName())
                    .customerRealName(customerRealInfoResponse == null ? "" :customerRealInfoResponse.getRealName())
                    .customerMobile(customerResponse.getMobile())
                    .build();

        } catch (Exception e) {
            log.error("获取客户信息失败，客户ID：{}，错误信息：{}", customerId, e.getMessage(), e);
            return null;
        }
    }

}
