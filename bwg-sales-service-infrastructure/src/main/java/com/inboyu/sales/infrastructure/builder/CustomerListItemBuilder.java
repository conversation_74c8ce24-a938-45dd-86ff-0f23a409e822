package com.inboyu.sales.infrastructure.builder;

import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.sales.infrastructure.dao.entity.PlatformCustomerEntity;
import com.inboyu.sales.infrastructure.dao.entity.ReserveSeeHouseEntity;
import com.inboyu.sales.infrastructure.dao.entity.SaleCycleEntity;

/**
 * 客户列表项构建器接口
 */
public interface CustomerListItemBuilder {

    /**
     * 构建客户列表项
     *
     * @param saleCycle 销售周期实体
     * @param platformCustomer 平台客户实体
     * @param reserveSeeHouse 预约看房实体
     * @return 客户列表项领域模型
     */
    CustomerListItem buildCustomerListItem(SaleCycleEntity saleCycle, 
                                          PlatformCustomerEntity platformCustomer, 
                                          ReserveSeeHouseEntity reserveSeeHouse);
}
