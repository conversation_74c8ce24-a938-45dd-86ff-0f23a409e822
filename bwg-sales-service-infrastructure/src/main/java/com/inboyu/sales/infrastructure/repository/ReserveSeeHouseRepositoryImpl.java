package com.inboyu.sales.infrastructure.repository;

import com.inboyu.constant.DeleteFlag;
import com.inboyu.sales.domain.reserve.model.ReserveSeeHouse;
import com.inboyu.sales.domain.reserve.model.SeeHouseStatus;
import com.inboyu.sales.domain.reserve.repository.ReserveSeeHouseRepository;
import com.inboyu.sales.infrastructure.dao.ReserveSeeHouseDao;
import com.inboyu.sales.infrastructure.dao.entity.ReserveSeeHouseEntity;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import com.inboyu.sales.infrastructure.builder.ReserveSeeHouseBuilder;

import java.time.LocalDate;
import java.util.List;

@Repository
public class ReserveSeeHouseRepositoryImpl implements ReserveSeeHouseRepository {

    @Autowired
    private ReserveSeeHouseDao reserveSeeHouseDao;

    @Autowired
    private ReserveSeeHouseBuilder reserveSeeHouseBuilder;


    @Override
    public Pagination<ReserveSeeHouse> pageReserveSeeHouse(Integer pageNum, Integer pageSize, Long customerId, Long storeId) {
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize);
        // 查询数据（支持多组织ID）
        Page<ReserveSeeHouseEntity> entityPage = reserveSeeHouseDao.pageByCustomerIdAndStoreId(customerId, storeId, pageable);

        // 转换为领域模型
        List<ReserveSeeHouse> stores = entityPage.getContent().stream()
                .map(reserveSeeHouseBuilder::toReserveSeeHouse)
                .toList();
        // 构造分页结果
        return new Pagination<>(
                entityPage.getNumber() + 1,
                entityPage.getSize(),
                entityPage.getTotalPages(),
                entityPage.getTotalElements(),
                stores);
    }

    /**
     * 保存预约记录
     *
     * @param reserve
     * @return {@link ReserveSeeHouse }
     */
    @Override
    public ReserveSeeHouse save(ReserveSeeHouse reserve) {
        ReserveSeeHouseEntity entity = reserveSeeHouseBuilder.toReserveSeeHouseEntity(reserve);
        ReserveSeeHouseEntity reserveSeeHouseEntity = reserveSeeHouseDao.save(entity);
        return reserveSeeHouseBuilder.toReserveSeeHouse(reserveSeeHouseEntity);
    }

    /**
     * 根据id查询预约记录
     *
     * @param ReserveSeeHouseId
     */
    @Override
    public ReserveSeeHouse findByReserveSeeHouseId(Long ReserveSeeHouseId) {
        ReserveSeeHouseEntity entity = reserveSeeHouseDao.findByReserveSeeHouseIdAndDeleted(ReserveSeeHouseId, DeleteFlag.NOT_DELETED);
        if (entity == null) {
            return null;
        }
        return reserveSeeHouseBuilder.toReserveSeeHouse(entity);
    }

    /**
     * 查询最新预约记录
     *
     * @param customerId
     * @param storeId
     * @param reserveDate
     * @return
     */
    @Override
    public ReserveSeeHouse findLatestBookedReservation(
            Long customerId,
            Long storeId,
            Long roomId,
            Long roomTypeId,
            LocalDate reserveDate
    ) {
        ReserveSeeHouseEntity entity = reserveSeeHouseDao.findLatestBookedReservation(
                customerId,
                storeId,
                roomId,
                roomTypeId,
                reserveDate,
                SeeHouseStatus.BOOKED.getCode()
        );
        if (entity == null) {
            return null;
        }
        return reserveSeeHouseBuilder.toReserveSeeHouse(entity);
    }

}
