package com.inboyu.sales.infrastructure.repository;

import com.inboyu.sales.domain.cycle.model.SaleCycle;
import com.inboyu.sales.domain.cycle.repository.SaleCycleRepository;
import com.inboyu.sales.infrastructure.builder.SaleCycleBuilder;
import com.inboyu.sales.infrastructure.dao.SaleCycleDao;
import com.inboyu.sales.infrastructure.dao.entity.SaleCycleEntity;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

// SaleCycleRepositoryImpl.java
@Repository
public class SaleCycleRepositoryImpl implements SaleCycleRepository {

    @Autowired
    private SaleCycleDao saleCycleDao;

    @Autowired
    private SaleCycleBuilder saleCycleBuilder;

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Override
    public SaleCycle findPlatformSaleCycle(Long platformCustomerId) {
        SaleCycleEntity entity = saleCycleDao.findPlatformSaleCycle(platformCustomerId);
        if (entity == null) {
            return null;
        }
        return saleCycleBuilder.toSaleCycle(entity);
    }

    @Override
    public SaleCycle findStoreSaleCycle(Long platformCustomerId, Long storeId, Long platformSaleCycleId) {
        SaleCycleEntity entity = saleCycleDao.findStoreSaleCycle(platformCustomerId, storeId, platformSaleCycleId);
        if (entity == null) {
            return null;
        }
        return saleCycleBuilder.toSaleCycle(entity);
    }

    @Override
    public SaleCycle save(SaleCycle saleCycle) {
        SaleCycleEntity entity = saleCycleBuilder.toSaleCycleEntity(saleCycle);
        if (entity.getSaleCycleId() == null) {
            entity.setSaleCycleId(snowflakeIdGenerator.nextId());
        }
        SaleCycleEntity saved = saleCycleDao.save(entity);
        return saleCycleBuilder.toSaleCycle(saved);
    }
}