// PlatformCustomerRepositoryImpl.java
package com.inboyu.sales.infrastructure.repository;

import com.inboyu.constant.DeleteFlag;
import com.inboyu.sales.domain.customer.model.PlatformCustomer;
import com.inboyu.sales.domain.customer.repository.PlatformCustomerRepository;
import com.inboyu.sales.infrastructure.builder.PlatformCustomerBuilder;
import com.inboyu.sales.infrastructure.dao.PlatformCustomerDao;
import com.inboyu.sales.infrastructure.dao.entity.PlatformCustomerEntity;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class PlatformCustomerRepositoryImpl implements PlatformCustomerRepository {

    @Autowired
    private PlatformCustomerDao platformCustomerDao;

    @Autowired
    private PlatformCustomerBuilder platformCustomerBuilder;

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Override
    public PlatformCustomer findByCustomerId(Long customerId) {
        PlatformCustomerEntity entity = platformCustomerDao.findByCustomerIdAndDeleted(customerId, DeleteFlag.NOT_DELETED);
        if (entity == null) {
            return null;
        }
        return platformCustomerBuilder.toPlatformCustomer(entity);
    }

    @Override
    public PlatformCustomer save(PlatformCustomer platformCustomer) {
        PlatformCustomerEntity entity = platformCustomerBuilder.toPlatformCustomerEntity(platformCustomer);
        if (entity.getPlatformCustomerId() == null) {
            entity.setPlatformCustomerId(snowflakeIdGenerator.nextId());
        }
        if (entity.getDeleted() == null) {
            entity.setDeleted(DeleteFlag.NOT_DELETED);
        }
        PlatformCustomerEntity saved = platformCustomerDao.save(entity);
        return platformCustomerBuilder.toPlatformCustomer(saved);
    }
}