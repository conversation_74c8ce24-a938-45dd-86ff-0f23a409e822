package com.inboyu.sales.infrastructure.builder.impl;

import com.inboyu.constant.DeleteFlag;
import com.inboyu.sales.domain.reserve.model.*;
import com.inboyu.sales.infrastructure.builder.ReserveSeeHouseBuilder;
import com.inboyu.sales.infrastructure.dao.entity.ReserveSeeHouseEntity;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ReserveSeeHouseBuilderImpl implements ReserveSeeHouseBuilder {

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Override
    public ReserveSeeHouse toReserveSeeHouse(ReserveSeeHouseEntity reserveSeeHouseEntity) {
        return ReserveSeeHouse.builder()
                .id(reserveSeeHouseEntity.getId())  // 修复：添加自增ID映射
                .reserveSeeHouseId(ReserveSeeHouseId.of(reserveSeeHouseEntity.getReserveSeeHouseId()))
                .customerId(CustomerId.of(reserveSeeHouseEntity.getCustomerId()))
                .storeId(StoreId.of(reserveSeeHouseEntity.getStoreId()))
                .roomTypeId(RoomTypeId.of(reserveSeeHouseEntity.getRoomTypeId()))
                .roomId(RoomId.of(reserveSeeHouseEntity.getRoomId()))
                .reserveDate(reserveSeeHouseEntity.getReserveDate())
                .startTime(reserveSeeHouseEntity.getStartTime())
                .endTime(reserveSeeHouseEntity.getEndTime())
                .staffId(StaffId.of(reserveSeeHouseEntity.getStaffId()))
                .seeState(reserveSeeHouseEntity.getSeeState())
                // 补全基础字段
                .createTime(reserveSeeHouseEntity.getCreateTime())
                .modifyTime(reserveSeeHouseEntity.getModifyTime())  // 修复：添加modifyTime映射
                .version(reserveSeeHouseEntity.getVersion())  // 修复：添加version映射
                .deleted(reserveSeeHouseEntity.getDeleted())  // 修复：添加deleted字段映射
                .build();
    }

    @Override
    public ReserveSeeHouseEntity toReserveSeeHouseEntity(ReserveSeeHouse reserveSeeHouse) {
        ReserveSeeHouseEntity entity = new ReserveSeeHouseEntity();

        // 关键修复：设置自增ID以确保JPA识别为更新操作
        if (reserveSeeHouse.getId() != null) {
            entity.setId(reserveSeeHouse.getId());
        }

        // 关键修复：设置版本号以确保乐观锁正常工作
        if (reserveSeeHouse.getVersion() != null) {
            entity.setVersion(reserveSeeHouse.getVersion());
        }

        // 关键修复：设置业务ID
        if (reserveSeeHouse.getReserveSeeHouseId() != null) {
            entity.setReserveSeeHouseId(reserveSeeHouse.getReserveSeeHouseId().getValue());
        } else {
            entity.setReserveSeeHouseId(snowflakeIdGenerator.nextId());
        }

        entity.setCustomerId(reserveSeeHouse.getCustomerId().getValue());
        entity.setStoreId(reserveSeeHouse.getStoreId().getValue());
        entity.setRoomTypeId(reserveSeeHouse.getRoomTypeId().getValue());
        entity.setRoomId(reserveSeeHouse.getRoomId().getValue());
        entity.setReserveDate(reserveSeeHouse.getReserveDate());
        entity.setStartTime(reserveSeeHouse.getStartTime());
        entity.setEndTime(reserveSeeHouse.getEndTime());
        entity.setStaffId(reserveSeeHouse.getStaffId().getValue());
        entity.setSeeState(reserveSeeHouse.getSeeState());
        entity.setDeleted(reserveSeeHouse.getDeleted() != null ? reserveSeeHouse.getDeleted() : DeleteFlag.NOT_DELETED);
        entity.setCreateTime(reserveSeeHouse.getCreateTime());
        entity.setModifyTime(reserveSeeHouse.getModifyTime());  // 修复：添加modifyTime设置

        return entity;
    }
}
