// BehaviorEventRepositoryImpl.java
package com.inboyu.sales.infrastructure.repository;

import com.inboyu.constant.DeleteFlag;
import com.inboyu.sales.domain.behavior.model.BehaviorEvent;
import com.inboyu.sales.domain.behavior.model.BehaviorEventId;
import com.inboyu.sales.domain.behavior.repository.BehaviorEventRepository;
import com.inboyu.sales.infrastructure.builder.BehaviorEventBuilder;
import com.inboyu.sales.infrastructure.dao.BehaviorEventDao;
import com.inboyu.sales.infrastructure.dao.entity.BehaviorEventEntity;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class BehaviorEventRepositoryImpl implements BehaviorEventRepository {

    @Autowired
    private BehaviorEventDao behaviorEventDao;

    @Autowired
    private BehaviorEventBuilder behaviorEventBuilder;

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Override
    public BehaviorEventId generateId() {
        return BehaviorEventId.of(snowflakeIdGenerator.nextId());
    }

    @Override
    public BehaviorEvent save(BehaviorEvent behaviorEvent) {
        BehaviorEventEntity entity = behaviorEventBuilder.toBehaviorEventEntity(behaviorEvent);
        if (entity.getBehaviorEventId() == null) {
            entity.setBehaviorEventId(snowflakeIdGenerator.nextId());
        }
        if (entity.getDeleted() == null) {
            entity.setDeleted(DeleteFlag.NOT_DELETED);
        }
        BehaviorEventEntity saved = behaviorEventDao.save(entity);
        return behaviorEventBuilder.toBehaviorEvent(saved);
    }
}