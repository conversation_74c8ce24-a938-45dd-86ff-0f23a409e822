package com.inboyu.sales.infrastructure.constant;

import lombok.Getter;

/**
 * 客户行为事件枚举
 */
@Getter
public enum EventTypeEnum {
    
    RESERVE_SEE_HOUSE("behavior_event_event_type_reserve_see_house", "预约看房");
    
    private final String code;
    private final String title;

    EventTypeEnum(String code, String title) {
        this.code = code;
        this.title = title;
    }

    public static EventTypeEnum getByCode(String code) {
        for (EventTypeEnum EventTypeEnum : EventTypeEnum.values()) {
            if (EventTypeEnum.getCode().equals(code)) {
                return EventTypeEnum;
            }
        }
        return null;
    }
}
