<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.inboyu</groupId>
        <artifactId>iby-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <artifactId>bwg-sales-service-infrastructure</artifactId>
    <version>${bwg.sales.service.version}</version>

    <dependencies>
        <dependency>
            <groupId>com.inboyu</groupId>
            <artifactId>iby-spring-cloud-starter-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.inboyu</groupId>
            <artifactId>iby-spring-cloud-starter-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.inboyu</groupId>
            <artifactId>iby-spring-cloud-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.inboyu</groupId>
            <artifactId>bwg-sales-service-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.inboyu</groupId>
            <artifactId>bwg-admin-service-api</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
