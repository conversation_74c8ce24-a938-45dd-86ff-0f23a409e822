package com.inboyu.sales.app.controller;

/**
 * <AUTHOR>
 * @date 2025/9/25
 */
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;

public class MemoryInfo {
    public static void main(String[] args) {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();

        // 堆内存使用情况
        MemoryUsage heapMemory = memoryBean.getHeapMemoryUsage();
        System.out.println("堆内存信息:");
        System.out.println("初始大小: " + (heapMemory.getInit() / 1024 / 1024) + " MB");
        System.out.println("已使用: " + (heapMemory.getUsed() / 1024 / 1024) + " MB");
        System.out.println("已提交: " + (heapMemory.getCommitted() / 1024 / 1024) + " MB");
        System.out.println("最大值: " + (heapMemory.getMax() / 1024 / 1024) + " MB");

        // 非堆内存使用情况
        MemoryUsage nonHeapMemory = memoryBean.getNonHeapMemoryUsage();
        System.out.println("\n非堆内存信息:");
        System.out.println("初始大小: " + (nonHeapMemory.getInit() / 1024 / 1024) + " MB");
        System.out.println("已使用: " + (nonHeapMemory.getUsed() / 1024 / 1024) + " MB");
        System.out.println("已提交: " + (nonHeapMemory.getCommitted() / 1024 / 1024) + " MB");
        System.out.println("最大值: " + (nonHeapMemory.getMax() / 1024 / 1024) + " MB");
    }
}
