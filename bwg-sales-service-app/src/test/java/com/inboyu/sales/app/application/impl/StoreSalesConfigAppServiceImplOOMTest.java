package com.inboyu.sales.app.application.impl;

import com.inboyu.sales.app.dto.converter.StoreSalesConfigAppConverter;
import com.inboyu.sales.app.dto.response.store.StoreSalesConfigDTO;
import com.inboyu.sales.domain.store.model.*;
import com.inboyu.sales.domain.store.service.StoreSalesConfigDomainService;
import com.inboyu.sales.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * StoreSalesConfigAppServiceImpl OOM问题修复测试
 * 
 * <AUTHOR>
 * @date 2025/9/25
 */
@ExtendWith(MockitoExtension.class)
class StoreSalesConfigAppServiceImplOOMTest {

    @Mock
    private StoreSalesConfigDomainService storeSalesConfigDomainService;

    @Mock
    private StoreSalesConfigAppConverter storeSalesConfigAppConverter;

    @InjectMocks
    private StoreSalesConfigAppServiceImpl storeSalesConfigAppService;

    private StoreSalesConfig normalStoreSalesConfig;
    private StoreSalesConfig abnormalStoreSalesConfig;

    @BeforeEach
    void setUp() {
        // 正常配置
        normalStoreSalesConfig = StoreSalesConfig.builder()
                .storeId(StoreId.of(1L))
                .seeHouseEnabled(SeeHouseEnabled.builder().code("status.enabled").build())
                .seeHouseDate(LocalDate.now())
                .seeHouseWeekDay(SeeHouseWeekDay.builder().code("store_sales_config_see_house_week_day.monday,store_sales_config_see_house_week_day.tuesday").build())
                .seeHouseTimeStart(LocalTime.of(9, 0))
                .seeHouseTimeEnd(LocalTime.of(18, 0))
                .timeEnabled(TimeEnabled.builder().code("status.enabled").build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder().code("status.enabled").build())
                .seeHouseValidDay(SeeHouseValidDay.builder().code("store_sales_config_see_house_valid_day.30").build())
                .build();

        // 异常配置 - 可能导致OOM的配置
        abnormalStoreSalesConfig = StoreSalesConfig.builder()
                .storeId(StoreId.of(2L))
                .seeHouseEnabled(SeeHouseEnabled.builder().code("status.enabled").build())
                .seeHouseDate(LocalDate.now())
                .seeHouseWeekDay(SeeHouseWeekDay.builder().code("store_sales_config_see_house_week_day.monday").build())
                .seeHouseTimeStart(LocalTime.of(0, 0))
                .seeHouseTimeEnd(LocalTime.of(23, 59))
                .timeEnabled(TimeEnabled.builder().code("status.enabled").build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder().code("status.enabled").build())
                .seeHouseValidDay(SeeHouseValidDay.builder().code("store_sales_config_see_house_valid_day.100").build()) // 异常大值
                .build();
    }

    @Test
    void testNormalConfiguration() {
        // Given
        when(storeSalesConfigDomainService.getStoreSalesConfigByStoreId(any())).thenReturn(normalStoreSalesConfig);
        when(storeSalesConfigAppConverter.toStoreSalesConfigDTO(any())).thenReturn(StoreSalesConfigDTO.builder().build());

        // When & Then - 正常配置应该能正常执行
        assertDoesNotThrow(() -> {
            StoreSalesConfigDTO result = storeSalesConfigAppService.getStoreSalesConfigByStoreId(1L);
            assertNotNull(result);
        });
    }

    @Test
    void testAbnormalValidDayConfiguration() {
        // Given - 异常的有效天数配置
        when(storeSalesConfigDomainService.getStoreSalesConfigByStoreId(any())).thenReturn(abnormalStoreSalesConfig);

        // When & Then - 应该抛出配置异常而不是OOM
        AppException exception = assertThrows(AppException.class, () -> {
            storeSalesConfigAppService.getStoreSalesConfigByStoreId(2L);
        });
        
        assertEquals(ResponseCode.STORE_SALES_CONFIG_INVALID.getCode(), exception.getCode());
        assertTrue(exception.getMessage().contains("看房有效天数配置异常"));
    }

    @Test
    void testSeeHouseValidDayProtection() {
        // Test SeeHouseValidDay.getDays() 防护机制
        
        // 测试正常值
        SeeHouseValidDay validDay7 = SeeHouseValidDay.builder().code("store_sales_config_see_house_valid_day.7").build();
        assertEquals(7, validDay7.getDays());
        
        SeeHouseValidDay validDay30 = SeeHouseValidDay.builder().code("store_sales_config_see_house_valid_day.30").build();
        assertEquals(30, validDay30.getDays());
        
        // 测试异常值 - 空值
        SeeHouseValidDay emptyDay = SeeHouseValidDay.builder().code("").build();
        assertThrows(IllegalArgumentException.class, emptyDay::getDays);
        
        // 测试异常值 - null
        SeeHouseValidDay nullDay = SeeHouseValidDay.builder().code(null).build();
        assertThrows(IllegalArgumentException.class, nullDay::getDays);
        
        // 测试异常值 - 超大值
        SeeHouseValidDay largeDay = SeeHouseValidDay.builder().code("store_sales_config_see_house_valid_day.100").build();
        assertThrows(IllegalArgumentException.class, largeDay::getDays);
        
        // 测试异常值 - 负值
        SeeHouseValidDay negativeDay = SeeHouseValidDay.builder().code("store_sales_config_see_house_valid_day.-1").build();
        assertThrows(IllegalArgumentException.class, negativeDay::getDays);
        
        // 测试异常值 - 无效格式
        SeeHouseValidDay invalidDay = SeeHouseValidDay.builder().code("invalid_format").build();
        assertThrows(IllegalArgumentException.class, invalidDay::getDays);
    }

    @Test
    void testTimeSpanProtection() {
        // 测试时间跨度防护机制
        StoreSalesConfig extremeTimeConfig = StoreSalesConfig.builder()
                .storeId(StoreId.of(3L))
                .seeHouseEnabled(SeeHouseEnabled.builder().code("status.enabled").build())
                .seeHouseDate(LocalDate.now())
                .seeHouseWeekDay(SeeHouseWeekDay.builder().code("store_sales_config_see_house_week_day.monday").build())
                .seeHouseTimeStart(LocalTime.of(0, 0))
                .seeHouseTimeEnd(LocalTime.of(23, 59)) // 接近24小时跨度
                .timeEnabled(TimeEnabled.builder().code("status.enabled").build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder().code("status.enabled").build())
                .seeHouseValidDay(SeeHouseValidDay.builder().code("store_sales_config_see_house_valid_day.7").build())
                .build();

        when(storeSalesConfigDomainService.getStoreSalesConfigByStoreId(any())).thenReturn(extremeTimeConfig);
        when(storeSalesConfigAppConverter.toStoreSalesConfigDTO(any())).thenReturn(StoreSalesConfigDTO.builder().build());

        // 应该能正常处理，不会因为时间跨度过大而OOM
        assertDoesNotThrow(() -> {
            StoreSalesConfigDTO result = storeSalesConfigAppService.getStoreSalesConfigByStoreId(3L);
            assertNotNull(result);
        });
    }

    @Test
    void testNullTimeConfiguration() {
        // 测试空时间配置
        StoreSalesConfig nullTimeConfig = StoreSalesConfig.builder()
                .storeId(StoreId.of(4L))
                .seeHouseEnabled(SeeHouseEnabled.builder().code("status.enabled").build())
                .seeHouseDate(LocalDate.now())
                .seeHouseWeekDay(SeeHouseWeekDay.builder().code("store_sales_config_see_house_week_day.monday").build())
                .seeHouseTimeStart(null)
                .seeHouseTimeEnd(null)
                .timeEnabled(TimeEnabled.builder().code("status.enabled").build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder().code("status.enabled").build())
                .seeHouseValidDay(SeeHouseValidDay.builder().code("store_sales_config_see_house_valid_day.7").build())
                .build();

        when(storeSalesConfigDomainService.getStoreSalesConfigByStoreId(any())).thenReturn(nullTimeConfig);

        // 应该抛出配置异常
        AppException exception = assertThrows(AppException.class, () -> {
            storeSalesConfigAppService.getStoreSalesConfigByStoreId(4L);
        });
        
        assertEquals(ResponseCode.STORE_SALES_CONFIG_INVALID.getCode(), exception.getCode());
        assertTrue(exception.getMessage().contains("看房时间配置异常"));
    }

    @Test
    void testInvalidTimeRangeConfiguration() {
        // 测试无效时间范围配置（开始时间晚于结束时间）
        StoreSalesConfig invalidTimeRangeConfig = StoreSalesConfig.builder()
                .storeId(StoreId.of(5L))
                .seeHouseEnabled(SeeHouseEnabled.builder().code("status.enabled").build())
                .seeHouseDate(LocalDate.now())
                .seeHouseWeekDay(SeeHouseWeekDay.builder().code("store_sales_config_see_house_week_day.monday").build())
                .seeHouseTimeStart(LocalTime.of(18, 0))
                .seeHouseTimeEnd(LocalTime.of(9, 0)) // 结束时间早于开始时间
                .timeEnabled(TimeEnabled.builder().code("status.enabled").build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder().code("status.enabled").build())
                .seeHouseValidDay(SeeHouseValidDay.builder().code("store_sales_config_see_house_valid_day.7").build())
                .build();

        when(storeSalesConfigDomainService.getStoreSalesConfigByStoreId(any())).thenReturn(invalidTimeRangeConfig);

        // 应该抛出配置异常
        AppException exception = assertThrows(AppException.class, () -> {
            storeSalesConfigAppService.getStoreSalesConfigByStoreId(5L);
        });
        
        assertEquals(ResponseCode.STORE_SALES_CONFIG_INVALID.getCode(), exception.getCode());
        assertTrue(exception.getMessage().contains("看房时间配置异常"));
    }
}
