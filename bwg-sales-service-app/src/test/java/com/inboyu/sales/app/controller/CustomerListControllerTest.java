package com.inboyu.sales.app.controller;

import com.alibaba.fastjson2.JSON;
import com.inboyu.sales.app.dto.response.customer.CustomerListResponseDTO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * <AUTHOR>
 * @date 2025/9/9
 */
@SpringBootTest
public class CustomerListControllerTest {

    @Resource
    private CustomerListController customerListController;

    @Test
    public void getCustomerList() {
        Integer pageNum = 1;
        Integer pageSize = 30;
        String storeId = "342232908947394560";

        CustomerListResponseDTO response = customerListController.getCustomerList(
                pageNum,
                pageSize,
                storeId
        );
        System.out.println(JSON.toJSONString(response));

        assertNotNull(response);
    }
}
