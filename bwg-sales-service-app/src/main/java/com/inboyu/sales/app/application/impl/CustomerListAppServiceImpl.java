package com.inboyu.sales.app.application.impl;

import com.inboyu.sales.app.application.CustomerListAppService;
import com.inboyu.sales.app.dto.converter.CustomerListAppConverter;
import com.inboyu.sales.app.dto.request.customer.CustomerListQueryRequestDTO;
import com.inboyu.sales.app.dto.response.customer.CustomerListResponseDTO;
import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.sales.domain.customer.service.CustomerListService;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客户列表应用服务实现
 */
@Service
@Slf4j
public class CustomerListAppServiceImpl implements CustomerListAppService {

    @Autowired
    private CustomerListService customerListService;

    @Autowired
    private CustomerListAppConverter customerListAppConverter;

    @Override
    public CustomerListResponseDTO getCustomerList(CustomerListQueryRequestDTO request) {
        log.info("应用层查询客户列表，请求参数：{}", request);

        Pagination<CustomerListItem> domainResult = customerListService.pageCustomerList(
                request.getPageNum(),
                request.getPageSize(),
                StoreId.of(request.getStoreId()));

        return customerListAppConverter.toResponseDTO(domainResult);
    }
}
