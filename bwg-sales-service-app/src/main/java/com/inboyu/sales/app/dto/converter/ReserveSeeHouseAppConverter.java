package com.inboyu.sales.app.dto.converter;

import com.inboyu.sales.app.dto.request.reserve.ReserveSeeHouseCreateRequestDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseCreateResponseDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseDetailResponseDTO;
import com.inboyu.sales.domain.reserve.model.*;
import com.inboyu.sales.domain.store.model.Store;
import com.inboyu.sales.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ReserveSeeHouseAppConverter {

    @Autowired
    CustomerInfoAppConverter customerInfoAppConverter;

    public ReserveSeeHouseDTO toDTO(ReserveSeeHouse reserveSeeHouse) {
        return ReserveSeeHouseDTO.builder()
                .reserveSeeHouseId(reserveSeeHouse.getReserveSeeHouseId().getValue().toString())
                .createTime(reserveSeeHouse.getCreateTime())
                .customerId(reserveSeeHouse.getCustomerId().getValue().toString())
                .staffId(reserveSeeHouse.getStaffId().toString())
                .storeId(reserveSeeHouse.getStoreId().getValue().toString())
                .roomTypeId(reserveSeeHouse.getRoomTypeId().toString())
                .roomId(reserveSeeHouse.getRoomId().toString())
                .reserveDate(reserveSeeHouse.getReserveDate())
                .startTime(reserveSeeHouse.getStartTime())
                .endTime(reserveSeeHouse.getEndTime())
                .seeState(reserveSeeHouse.getSeeState())
                .build();
    }

    /**
     * 将创建请求DTO转换为领域模型
     */
    public ReserveSeeHouse toDomainFromCreateRequest(ReserveSeeHouseCreateRequestDTO dto) {

        // 添加必要字段的空值检查
        if (dto.getCustomerId() == null || dto.getStoreId() == null) {
            throw new AppException(ResponseCode.RESERVE_SEE_HOUSE_CUSTOMER_ID_OR_STORE_ID_NULL);
        }

        return ReserveSeeHouse.builder()
                .customerId(CustomerId.of(dto.getCustomerId()))
                .staffId(StaffId.of(parseToLongOrDefault(dto.getStaffId(), 0L)))
                .storeId(StoreId.of(dto.getStoreId()))
                .roomTypeId(RoomTypeId.of(parseToLongOrDefault(dto.getRoomTypeId(), 0L)))
                .roomId(RoomId.of(parseToLongOrDefault(dto.getRoomId(), 0L)))
                .reserveDate(dto.getReserveDate())
                .startTime(dto.getStartTime())
                .endTime(dto.getEndTime())
                .seeState(StringUtils.isNotBlank(dto.getSeeState()) ? dto.getSeeState() : SeeHouseStatus.BOOKED.getCode())
                .build();
    }

    /**
     * 将领域模型转换为创建响应DTO
     */
    public ReserveSeeHouseCreateResponseDTO toCreateResponseDTO(ReserveSeeHouse reserveSeeHouse) {
        return  ReserveSeeHouseCreateResponseDTO.builder()
                .reserveSeeHouseId(reserveSeeHouse.getReserveSeeHouseId().getValue().toString())
                .customerId(reserveSeeHouse.getCustomerId().toString())
                .staffId(reserveSeeHouse.getStaffId().toString())
                .storeId(reserveSeeHouse.getStoreId().toString())
                .roomTypeId(reserveSeeHouse.getRoomTypeId().toString())
                .roomId(reserveSeeHouse.getRoomId().toString())
                .reserveDate(reserveSeeHouse.getReserveDate())
                .startTime(reserveSeeHouse.getStartTime())
                .endTime(reserveSeeHouse.getEndTime())
                .seeState(reserveSeeHouse.getSeeState())
                .build();
    }

    public ReserveSeeHouseDetailResponseDTO toDetailResponseDTO(ReserveSeeHouse reserveSeeHouse) {
        ReserveSeeHouseDetailResponseDTO.ReserveSeeHouseDetailResponseDTOBuilder builder = ReserveSeeHouseDetailResponseDTO.builder()
                .reserveSeeHouseId(reserveSeeHouse.getReserveSeeHouseId().getValue().toString())
                .customerId(reserveSeeHouse.getCustomerId().toString())
                .staffId(reserveSeeHouse.getStaffId().toString())
                .storeId(reserveSeeHouse.getStoreId().toString())
                .roomTypeId(reserveSeeHouse.getRoomTypeId().toString())
                .roomId(reserveSeeHouse.getRoomId().toString())
                .reserveDate(reserveSeeHouse.getReserveDate())
                .startTime(reserveSeeHouse.getStartTime())
                .endTime(reserveSeeHouse.getEndTime())
                .seeState(reserveSeeHouse.getSeeState());

        if (reserveSeeHouse.getCustomerInfo() != null) {
            builder.customerInfo(customerInfoAppConverter.toResponseDTO(reserveSeeHouse.getCustomerInfo()));
        }
        return builder.build();
    }

    /**
     * 将领域模型转换为详情响应DTO（包含门店信息）
     */
    public ReserveSeeHouseDetailResponseDTO toDetailResponseDTO(ReserveSeeHouse reserveSeeHouse, Store storeDetail) {
        ReserveSeeHouseDetailResponseDTO.ReserveSeeHouseDetailResponseDTOBuilder builder = ReserveSeeHouseDetailResponseDTO.builder()
                .reserveSeeHouseId(reserveSeeHouse.getReserveSeeHouseId().getValue().toString())
                .customerId(reserveSeeHouse.getCustomerId().getValue().toString())
                .staffId(reserveSeeHouse.getStaffId().getValue().toString())
                .storeId(reserveSeeHouse.getStoreId().toString())
                .roomTypeId(reserveSeeHouse.getRoomTypeId().toString())
                .roomId(reserveSeeHouse.getRoomId().toString())
                .reserveDate(reserveSeeHouse.getReserveDate())
                .startTime(reserveSeeHouse.getStartTime())
                .endTime(reserveSeeHouse.getEndTime())
                .seeState(reserveSeeHouse.getSeeState());

        if (reserveSeeHouse.getCustomerInfo() != null) {
            builder.customerInfo(customerInfoAppConverter.toResponseDTO(reserveSeeHouse.getCustomerInfo()));
        }

        // 如果门店信息不为空，则设置门店相关字段
        if (storeDetail != null) {
            builder.storeName(storeDetail.getStoreName())
                   .storeAddress(storeDetail.getStoreAddress());
        }

        return builder.build();
    }


    /**
     * 将字符串或null转换为Long，如果为空则使用默认值
     */
    private Long parseToLongOrDefault(Object value, Long defaultValue) {
        if (value == null) {
            return defaultValue;
        }

        if (value instanceof String) {
            String strValue = (String) value;
            if (StringUtils.isBlank(strValue)) {
                return defaultValue;
            }
            try {
                return Long.valueOf(strValue);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }

        if (value instanceof Long) {
            return (Long) value;
        }

        if (value instanceof Integer) {
            return ((Integer) value).longValue();
        }

        return defaultValue;
    }
}
