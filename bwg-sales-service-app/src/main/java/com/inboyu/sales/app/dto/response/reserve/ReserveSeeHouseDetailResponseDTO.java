package com.inboyu.sales.app.dto.response.reserve;

import com.inboyu.sales.app.dto.response.customer.CustomerInfoResponseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 预约看房响应DTO
 */
@Data
@Builder
@Schema(description = "预约看房信息")
public class ReserveSeeHouseDetailResponseDTO {

    @Schema(description = "预约看房记录id")
    private String reserveSeeHouseId;

    @Schema(description = "客户ID")
    private String customerId;

    @Schema(description = "员工ID")
    private String staffId;

    @Schema(description = "门店ID")
    private String storeId;

    @Schema(description = "户型ID")
    private String roomTypeId;

    @Schema(description = "房间ID")
    private String roomId;

    @Schema(description = "预约日期")
    private LocalDate reserveDate;

    @Schema(description = "开始时间")
    private LocalTime startTime;

    @Schema(description = "结束时间")
    private LocalTime endTime;

    @Schema(description = "看房状态,字典维护")
    private String seeState;

    @Schema(description = "门店名称")
    private String storeName;

    @Schema(description = "门店地址")
    private String storeAddress;

    @Schema(description = "用户信息")
    CustomerInfoResponseDTO customerInfo;
}

