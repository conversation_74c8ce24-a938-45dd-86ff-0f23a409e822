package com.inboyu.sales.app.dto.response.customer;

import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * 客户列表响应DTO
 */
@Data
@Schema(description = "客户列表响应")
public class CustomerListResponseDTO extends Pagination<CustomerCardDTO> {

    public CustomerListResponseDTO(Integer pageNum, Integer pageSize, Integer totalPages, Long total, List<CustomerCardDTO> list) {
        super(pageNum, pageSize, totalPages, total, list);
    }
}
