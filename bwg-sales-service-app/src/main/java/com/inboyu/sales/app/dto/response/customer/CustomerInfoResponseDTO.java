package com.inboyu.sales.app.dto.response.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客户信息响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "客户信息响应")
public class CustomerInfoResponseDTO {

    @Schema(description = "客户ID")
    private String customerId;

    @Schema(description = "客户名称", example = "张三")
    private String customerName;

    @Schema(description = "客户真实姓名", example = "张三")
    private String customerRealName;

    @Schema(description = "客户手机号码")
    private String mobile;
}
