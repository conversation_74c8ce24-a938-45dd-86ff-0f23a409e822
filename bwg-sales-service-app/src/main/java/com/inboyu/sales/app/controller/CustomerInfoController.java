package com.inboyu.sales.app.controller;

import com.inboyu.sales.app.application.CustomerInfoAppService;
import com.inboyu.sales.app.dto.response.customer.CustomerInfoResponseDTO;
import com.inboyu.spring.cloud.starter.context.base.BaseContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 客户信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/customer-info")
@Tag(name = "客户列表控制器")
public class CustomerInfoController {

    @Autowired
    private CustomerInfoAppService customerInfoAppService;

    @GetMapping
    @Operation(summary = "获取客户信息", description = "获取客户信息")
    public CustomerInfoResponseDTO getCustomerInfo(@RequestParam @Schema(description = "客户id") String customerId) {

        log.info("获取客户信息，用户ID：{}，用户类型：{}", BaseContext.getLoginUserId(), BaseContext.getLoginUserType());

        if (ObjectUtils.isEmpty(customerId)) {
            throw new IllegalArgumentException("客户ID不能为空");
        }

        CustomerInfoResponseDTO response = customerInfoAppService.getCustomerInfo(Long.valueOf(customerId));

        log.info("客户信息: {}", response);

        return response;
    }
}
