package com.inboyu.sales.app.application;

import com.inboyu.sales.app.dto.request.customer.CustomerListQueryRequestDTO;
import com.inboyu.sales.app.dto.response.customer.CustomerInfoResponseDTO;
import com.inboyu.sales.app.dto.response.customer.CustomerListResponseDTO;
import com.inboyu.sales.domain.customer.model.CustomerId;

/**
 * 客户信息应用服务接口
 */
public interface CustomerInfoAppService {

    /**
     * 获取当前客户信息
     *
     * @return 客户响应
     */
    CustomerInfoResponseDTO getCustomerInfo(Long customerId);
}
