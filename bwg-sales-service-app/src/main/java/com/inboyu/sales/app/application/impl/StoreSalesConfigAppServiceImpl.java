package com.inboyu.sales.app.application.impl;

import com.inboyu.sales.app.application.StoreSalesConfigAppService;
import com.inboyu.sales.app.dto.converter.StoreSalesConfigAppConverter;
import com.inboyu.sales.app.dto.request.store.CreateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.request.store.UpdateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.response.store.SeeHouseDTO;
import com.inboyu.sales.app.dto.response.store.StoreSalesConfigDTO;
import com.inboyu.sales.app.dto.response.store.TimePointDTO;
import com.inboyu.sales.domain.store.model.SeeHouseWeekDay;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.domain.store.model.StoreSalesConfig;
import com.inboyu.sales.domain.store.service.StoreSalesConfigDomainService;
import com.inboyu.sales.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class StoreSalesConfigAppServiceImpl implements StoreSalesConfigAppService {

    @Autowired
    private StoreSalesConfigDomainService storeSalesConfigDomainService;

    @Autowired
    private StoreSalesConfigAppConverter storeSalesConfigAppConverter;

    @Override
    public StoreSalesConfigDTO createStoreSalesConfig(CreateStoreSalesConfigRequestDTO request) {
        log.info("创建门店销售配置，门店ID: {}", request.getStoreId());
        
        StoreSalesConfig storeSalesConfig = storeSalesConfigAppConverter.toStoreSalesConfig(request);
        StoreSalesConfig createdConfig = storeSalesConfigDomainService.createStoreSalesConfig(storeSalesConfig);
        
        return storeSalesConfigAppConverter.toStoreSalesConfigDTO(createdConfig);
    }

    @Override
    public StoreSalesConfigDTO getStoreSalesConfigByStoreId(Long storeId) {
        log.info("根据门店ID查询销售配置，门店ID: {}", storeId);
        StoreSalesConfig storeSalesConfig = storeSalesConfigDomainService.getStoreSalesConfigByStoreId(StoreId.of(storeId));
        if (storeSalesConfig == null) {
            throw new AppException(ResponseCode.STORE_SALES_CONFIG_NOT_FOUND);
        }

        StoreSalesConfigDTO dto = storeSalesConfigAppConverter.toStoreSalesConfigDTO(storeSalesConfig);

        // seeHouseList 计算逻辑
        List<SeeHouseDTO> seeHouseList = new ArrayList<>();
        if (storeSalesConfig.getSeeHouseEnabled().isEnabled()) {

            LocalDate sysDate = LocalDate.now();
            if (!storeSalesConfig.getSeeHouseTodayEnabled().isEnabled()) {
                sysDate = LocalDate.now().plusDays(1);
            }

            LocalDate configStartDate = storeSalesConfig.getSeeHouseDate();
            int validDay = storeSalesConfig.getSeeHouseValidDay().getDays();
            LocalTime timeStart = storeSalesConfig.getSeeHouseTimeStart();
            LocalTime timeEnd = storeSalesConfig.getSeeHouseTimeEnd();

            // 解析可预约星期
            String weekDayStr = storeSalesConfig.getSeeHouseWeekDay().getCode();
            List<Integer> weekDays = new ArrayList<>();
            if (weekDayStr != null && !weekDayStr.isEmpty()) {
                for (String s : weekDayStr.split(",")) {
                    int day = SeeHouseWeekDay.codeToDayOfWeek(s.trim());
                    if (day > 0) {
                        weekDays.add(day);
                    }
                }
            }

            // 计算实际开始日期和数量
            LocalDate startDate = configStartDate.isBefore(sysDate) ? sysDate : configStartDate;
            int days = validDay;
            if (configStartDate.isBefore(sysDate)) {
                int remain = validDay - (int) (sysDate.toEpochDay() - configStartDate.toEpochDay());
                days = Math.max(remain, 0);
            }

            for (int i = 0; i < days; i++) {
                LocalDate date = startDate.plusDays(i);
                int weekValue = date.getDayOfWeek().getValue(); // 1=周一,7=周日
                int canReserve = weekDays.contains(weekValue) ? 1 : 0;
                SeeHouseDTO.SeeHouseDTOBuilder builder = SeeHouseDTO.builder();
                builder.date(date);
                builder.showDate(date.format(DateTimeFormatter.ofPattern("MM/dd")));
                builder.canReserve(canReserve);
                builder.weekName(calcWeekName(date, sysDate));
                List<String> timeBuckets = calcTimeBuckets(timeStart, timeEnd);
                builder.timeBuckets(timeBuckets);
                if (storeSalesConfig.getTimeEnabled().isEnabled()) {
                    builder.timePoints(calcTimePoints(date, timeStart, timeEnd));
                }
                seeHouseList.add(builder.build());
            }
        }
        dto.setSeeHouseList(seeHouseList);
        return dto;
    }

    private String calcWeekName(LocalDate date, LocalDate sysDate) {
        if (date.equals(sysDate)) {
            return "今天";
        } else if (date.equals(sysDate.plusDays(1))) {
            return "明天";
        } else {
            String[] weekNames = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
            return weekNames[date.getDayOfWeek().getValue() - 1];
        }
    }

    private List<String> calcTimeBuckets(LocalTime timeStart, LocalTime timeEnd) {
        List<String> buckets = new ArrayList<>();
        LocalTime current = timeStart;

        // 如果起始时间不是整点，先补齐到下一个整点
        if (current.getMinute() != 0) {
            LocalTime nextHour = current.withMinute(0).plusHours(1);
            if (nextHour.isAfter(timeEnd)) nextHour = timeEnd;
            buckets.add(String.format("%s-%s", current, nextHour));
            current = nextHour;
        }

        // 每小时分段
        while (current.plusHours(1).isBefore(timeEnd) || current.plusHours(1).equals(timeEnd)) {
            LocalTime next = current.plusHours(1);
            buckets.add(String.format("%s-%s", current, next));
            current = next;
        }

        // 处理最后一个非整点时间段
        if (current.isBefore(timeEnd)) {
            buckets.add(String.format("%s-%s", current, timeEnd));
        }

        return buckets;
    }

    private List<TimePointDTO> calcTimePoints(LocalDate date, LocalTime timeStart, LocalTime timeEnd) {
        List<TimePointDTO> points = new ArrayList<>();
        LocalTime current = timeStart;
        while (current.isBefore(timeEnd)) {
            LocalTime next = current.plusHours(1);
            if (next.isAfter(timeEnd)) next = timeEnd;
            points.add(TimePointDTO.builder()
                    .point(current.toString())
                    .period(String.format("%s %s-%s", date, current, next))
                    .canSelect(1)
                    .build());
            current = next;
        }
        return points;
    }

    @Override
    public StoreSalesConfigDTO updateStoreSalesConfig(UpdateStoreSalesConfigRequestDTO updateStoreSalesConfigRequestDTO) {
        log.info("更新门店销售配置，ID: {}", updateStoreSalesConfigRequestDTO.getStoreId());

        StoreSalesConfig storeSalesConfig = storeSalesConfigAppConverter.toStoreSalesConfig(updateStoreSalesConfigRequestDTO);
        storeSalesConfigDomainService.updateStoreSalesConfig(storeSalesConfig);

        return storeSalesConfigAppConverter.toStoreSalesConfigDTO(storeSalesConfig);
    }
}
