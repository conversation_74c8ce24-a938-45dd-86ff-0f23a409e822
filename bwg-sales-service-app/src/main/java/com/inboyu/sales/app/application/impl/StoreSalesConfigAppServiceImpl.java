package com.inboyu.sales.app.application.impl;

import com.inboyu.sales.app.application.StoreSalesConfigAppService;
import com.inboyu.sales.app.dto.converter.StoreSalesConfigAppConverter;
import com.inboyu.sales.app.dto.request.store.CreateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.request.store.UpdateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.response.store.SeeHouseDTO;
import com.inboyu.sales.app.dto.response.store.StoreSalesConfigDTO;
import com.inboyu.sales.app.dto.response.store.TimePointDTO;
import com.inboyu.sales.domain.store.model.SeeHouseWeekDay;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.domain.store.model.StoreSalesConfig;
import com.inboyu.sales.domain.store.service.StoreSalesConfigDomainService;
import com.inboyu.sales.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class StoreSalesConfigAppServiceImpl implements StoreSalesConfigAppService {

    @Autowired
    private StoreSalesConfigDomainService storeSalesConfigDomainService;

    @Autowired
    private StoreSalesConfigAppConverter storeSalesConfigAppConverter;

    // 时间段缓存，避免重复计算相同的时间段
    private final Map<String, List<String>> timeBucketsCache = new ConcurrentHashMap<>();

    // 缓存键生成
    private String generateTimeBucketsCacheKey(LocalTime timeStart, LocalTime timeEnd) {
        return timeStart.toString() + "-" + timeEnd.toString();
    }

    @Override
    public StoreSalesConfigDTO createStoreSalesConfig(CreateStoreSalesConfigRequestDTO request) {
        log.info("创建门店销售配置，门店ID: {}", request.getStoreId());
        
        StoreSalesConfig storeSalesConfig = storeSalesConfigAppConverter.toStoreSalesConfig(request);
        StoreSalesConfig createdConfig = storeSalesConfigDomainService.createStoreSalesConfig(storeSalesConfig);
        
        return storeSalesConfigAppConverter.toStoreSalesConfigDTO(createdConfig);
    }

    @Override
    public StoreSalesConfigDTO getStoreSalesConfigByStoreId(Long storeId) {
        log.info("根据门店ID查询销售配置，门店ID: {}", storeId);
        StoreSalesConfig storeSalesConfig = storeSalesConfigDomainService.getStoreSalesConfigByStoreId(StoreId.of(storeId));
        if (storeSalesConfig == null) {
            throw new AppException(ResponseCode.STORE_SALES_CONFIG_NOT_FOUND);
        }

        StoreSalesConfigDTO dto = storeSalesConfigAppConverter.toStoreSalesConfigDTO(storeSalesConfig);

        // seeHouseList 计算逻辑
        List<SeeHouseDTO> seeHouseList = new ArrayList<>();
        if (storeSalesConfig.getSeeHouseEnabled().isEnabled()) {

            LocalDate sysDate = LocalDate.now();
            if (!storeSalesConfig.getSeeHouseTodayEnabled().isEnabled()) {
                sysDate = LocalDate.now().plusDays(1);
            }

            LocalDate configStartDate = storeSalesConfig.getSeeHouseDate();
            int validDay = storeSalesConfig.getSeeHouseValidDay().getDays();
            LocalTime timeStart = storeSalesConfig.getSeeHouseTimeStart();
            LocalTime timeEnd = storeSalesConfig.getSeeHouseTimeEnd();

            // 添加业务参数校验，防止异常数据导致OOM
            if (validDay <= 0 || validDay > 30) {
                log.warn("看房有效天数配置异常: {}, 门店ID: {}", validDay, storeId);
                throw new AppException(ResponseCode.STORE_SALES_CONFIG_INVALID, "看房有效天数配置异常");
            }

            if (timeStart == null || timeEnd == null || timeStart.isAfter(timeEnd)) {
                log.warn("看房时间配置异常: {} - {}, 门店ID: {}", timeStart, timeEnd, storeId);
                throw new AppException(ResponseCode.STORE_SALES_CONFIG_INVALID, "看房时间配置异常");
            }

            // 解析可预约星期
            String weekDayStr = storeSalesConfig.getSeeHouseWeekDay().getCode();
            List<Integer> weekDays = new ArrayList<>();
            if (weekDayStr != null && !weekDayStr.isEmpty()) {
                for (String s : weekDayStr.split(",")) {
                    int day = SeeHouseWeekDay.codeToDayOfWeek(s.trim());
                    if (day > 0) {
                        weekDays.add(day);
                    }
                }
            }

            // 计算实际开始日期和数量
            LocalDate startDate = configStartDate.isBefore(sysDate) ? sysDate : configStartDate;
            int days = validDay;
            if (configStartDate.isBefore(sysDate)) {
                int remain = validDay - (int) (sysDate.toEpochDay() - configStartDate.toEpochDay());
                days = Math.max(remain, 0);
            }

            // 使用缓存的时间段计算，避免在循环中重复计算
            String cacheKey = generateTimeBucketsCacheKey(timeStart, timeEnd);
            List<String> timeBuckets = timeBucketsCache.computeIfAbsent(cacheKey,
                k -> calcTimeBuckets(timeStart, timeEnd));
            boolean timeEnabled = storeSalesConfig.getTimeEnabled().isEnabled();

            // 预分配集合容量，提高性能
            seeHouseList = new ArrayList<>(days);

            for (int i = 0; i < days; i++) {
                LocalDate date = startDate.plusDays(i);
                int weekValue = date.getDayOfWeek().getValue(); // 1=周一,7=周日
                int canReserve = weekDays.contains(weekValue) ? 1 : 0;

                SeeHouseDTO.SeeHouseDTOBuilder builder = SeeHouseDTO.builder()
                        .date(date)
                        .showDate(date.format(DateTimeFormatter.ofPattern("MM/dd")))
                        .canReserve(canReserve)
                        .weekName(calcWeekName(date, sysDate))
                        .timeBuckets(timeBuckets); // 复用预计算的时间段

                if (timeEnabled) {
                    builder.timePoints(calcTimePoints(date, timeStart, timeEnd));
                }
                seeHouseList.add(builder.build());
            }
        }
        dto.setSeeHouseList(seeHouseList);
        return dto;
    }

    private String calcWeekName(LocalDate date, LocalDate sysDate) {
        if (date.equals(sysDate)) {
            return "今天";
        } else if (date.equals(sysDate.plusDays(1))) {
            return "明天";
        } else {
            String[] weekNames = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
            return weekNames[date.getDayOfWeek().getValue() - 1];
        }
    }

    private List<String> calcTimeBuckets(LocalTime timeStart, LocalTime timeEnd) {
        // 添加防护机制，避免时间跨度过大导致OOM
        if (timeStart == null || timeEnd == null || timeStart.isAfter(timeEnd)) {
            return new ArrayList<>();
        }

        // 计算时间跨度，限制最大小时数防止内存溢出
        long hoursBetween = java.time.Duration.between(timeStart, timeEnd).toHours();
        if (hoursBetween > 24) {
            log.warn("时间跨度过大: {}小时，限制为24小时", hoursBetween);
            hoursBetween = 24;
            timeEnd = timeStart.plusHours(24);
        }

        // 预分配集合容量，提高性能
        List<String> buckets = new ArrayList<>(24);
        LocalTime current = timeStart;

        // 添加循环计数器，防止无限循环
        int loopCount = 0;
        final int MAX_LOOP_COUNT = 24; // 最多26次循环（24小时+边界处理）

        // 如果起始时间不是整点，先补齐到下一个整点
        if (current.getMinute() != 0) {
            LocalTime nextHour = current.withMinute(0).plusHours(1);
            if (nextHour.isAfter(timeEnd)) nextHour = timeEnd;
            buckets.add(String.format("%s-%s", current, nextHour));
            current = nextHour;
        }

        // 每小时分段
        while ((current.plusHours(1).isBefore(timeEnd) || current.plusHours(1).equals(timeEnd))
               && loopCount < MAX_LOOP_COUNT) {
            LocalTime next = current.plusHours(1);
            buckets.add(String.format("%s-%s", current, next));
            current = next;
            loopCount++;
        }

        // 处理最后一个非整点时间段
        if (current.isBefore(timeEnd) && loopCount < MAX_LOOP_COUNT) {
            buckets.add(String.format("%s-%s", current, timeEnd));
            loopCount++;
        }

        if (loopCount >= MAX_LOOP_COUNT) {
            log.warn("时间段计算达到最大循环次数限制: {}", MAX_LOOP_COUNT);
        }

        return buckets;
    }

    private List<TimePointDTO> calcTimePoints(LocalDate date, LocalTime timeStart, LocalTime timeEnd) {
        // 添加防护机制，避免时间跨度过大导致OOM
        if (timeStart == null || timeEnd == null || timeStart.isAfter(timeEnd)) {
            return new ArrayList<>();
        }

        // 计算时间跨度，限制最大小时数防止内存溢出
        long hoursBetween = java.time.Duration.between(timeStart, timeEnd).toHours();
        if (hoursBetween > 24) {
            log.warn("时间跨度过大: {}小时，限制为24小时", hoursBetween);
            hoursBetween = 24;
            timeEnd = timeStart.plusHours(24);
        }

        // 预分配集合容量，提高性能
        List<TimePointDTO> points = new ArrayList<>((int) hoursBetween + 1);
        LocalTime current = timeStart;

        // 添加循环计数器，防止无限循环
        int loopCount = 0;
        final int MAX_LOOP_COUNT = 25; // 最多25次循环（24小时+1）

        while (current.isBefore(timeEnd) && loopCount < MAX_LOOP_COUNT) {
            LocalTime next = current.plusHours(1);
            if (next.isAfter(timeEnd)) next = timeEnd;

            points.add(TimePointDTO.builder()
                    .point(current.toString())
                    .period(String.format("%s %s-%s", date, current, next))
                    .canSelect(1)
                    .build());
            current = next;
            loopCount++;
        }

        if (loopCount >= MAX_LOOP_COUNT) {
            log.warn("时间点计算达到最大循环次数限制: {}", MAX_LOOP_COUNT);
        }

        return points;
    }

    @Override
    public StoreSalesConfigDTO updateStoreSalesConfig(UpdateStoreSalesConfigRequestDTO updateStoreSalesConfigRequestDTO) {
        log.info("更新门店销售配置，ID: {}", updateStoreSalesConfigRequestDTO.getStoreId());

        StoreSalesConfig storeSalesConfig = storeSalesConfigAppConverter.toStoreSalesConfig(updateStoreSalesConfigRequestDTO);
        storeSalesConfigDomainService.updateStoreSalesConfig(storeSalesConfig);

        return storeSalesConfigAppConverter.toStoreSalesConfigDTO(storeSalesConfig);
    }
}
