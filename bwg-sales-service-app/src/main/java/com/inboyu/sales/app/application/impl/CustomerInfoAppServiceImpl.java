package com.inboyu.sales.app.application.impl;

import com.inboyu.sales.app.application.CustomerInfoAppService;
import com.inboyu.sales.app.dto.converter.CustomerInfoAppConverter;
import com.inboyu.sales.app.dto.response.customer.CustomerInfoResponseDTO;
import com.inboyu.sales.domain.customer.model.CustomerId;
import com.inboyu.sales.domain.customer.model.CustomerInfo;
import com.inboyu.sales.domain.customer.service.CustomerInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客户信息应用服务实现
 */
@Service
@Slf4j
public class CustomerInfoAppServiceImpl implements CustomerInfoAppService {

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private CustomerInfoAppConverter customerInfoAppConverter;



    @Override
    public CustomerInfoResponseDTO getCustomerInfo(Long customerId) {
        log.info("应用层查询客户信息，请求参数：{}", customerId);

        if (customerId == null) {
            throw new IllegalArgumentException("客户ID不能为空");
        }

        CustomerInfo domainResult = customerInfoService.getCustomerInfo(CustomerId.of(customerId));
        if (domainResult == null) {
            return null;
        }

        return customerInfoAppConverter.toResponseDTO(domainResult);
    }
}
