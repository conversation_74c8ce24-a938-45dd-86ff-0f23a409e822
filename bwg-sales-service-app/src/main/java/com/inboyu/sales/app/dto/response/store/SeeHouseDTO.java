package com.inboyu.sales.app.dto.response.store;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@Schema(description = "销售配置时间详情")
public class SeeHouseDTO {

    private LocalDate date;

    private String showDate;

    private int canReserve;

    //weekName 今天 明天 周四
    private String weekName;

    List<String> timeBuckets;

    List<TimePointDTO> timePoints;

}
