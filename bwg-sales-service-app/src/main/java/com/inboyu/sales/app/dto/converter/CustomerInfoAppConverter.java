package com.inboyu.sales.app.dto.converter;

import com.inboyu.sales.app.dto.response.customer.CustomerCardDTO;
import com.inboyu.sales.app.dto.response.customer.CustomerInfoResponseDTO;
import com.inboyu.sales.app.dto.response.customer.CustomerListResponseDTO;
import com.inboyu.sales.app.dto.response.customer.CustomerReservationDTO;
import com.inboyu.sales.domain.customer.model.CustomerInfo;
import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.sales.domain.customer.model.CustomerReservation;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户信息应用层转换器
 */
@Component
public class CustomerInfoAppConverter {

    /**
     * 领域模型转换为DTO
     */
    public CustomerInfoResponseDTO toResponseDTO(CustomerInfo customerInfo) {
        return new CustomerInfoResponseDTO(
                String.valueOf(customerInfo.getCustomerId().getValue()),
                customerInfo.getCustomerName(),
                customerInfo.getCustomerRealName(),
                customerInfo.getCustomerMobile()
        );
    }

}
