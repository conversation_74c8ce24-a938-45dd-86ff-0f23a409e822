package com.inboyu.sales.app.dto.response.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客户卡片DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "客户卡片信息")
public class CustomerCardDTO {

    @Schema(description = "平台客户ID")
    private String platformCustomerId;

    @Schema(description = "客户ID")
    private String customerId;

    @Schema(description = "客户名称", example = "张三")
    private String customerName;

    @Schema(description = "门店ID")
    private String storeId;

    @Schema(description = "销售周期开始时间")
    private String saleCycleStartTime;

    @Schema(description = "预约看房记录")
    private CustomerReservationDTO reservation;
}
