package com.inboyu.sales.app.controller;

import com.inboyu.sales.app.application.CustomerListAppService;
import com.inboyu.sales.app.dto.request.customer.CustomerListQueryRequestDTO;
import com.inboyu.sales.app.dto.response.customer.CustomerListResponseDTO;
import com.inboyu.spring.cloud.starter.context.base.BaseContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 客户列表控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/customer-list")
@Tag(name = "运营端-客户列表控制器")
public class CustomerListController {

    @Autowired
    private CustomerListAppService customerListAppService;

    @GetMapping
    @Operation(summary = "运营端-分页查询指定门店的客户列表", description = "查询指定门店的分页客户列表")
    public CustomerListResponseDTO getCustomerList(
            @RequestParam
            @Parameter(description = "当前页", example = "1")
            Integer pageNum,
            @RequestParam
            @Parameter(description = "每页条数", example = "30")
            @Min(1)
            @Max(50)
            Integer pageSize,
            @RequestParam
            @Parameter(description = "门店ID", example = "342232754680893440")
            @NotEmpty(message = "门店ID不能为空")
            String storeId
    ) {
        
        log.info("查询客户列表，页码：{}，每页条数：{}，门店ID：{}，用户ID：{}，用户类型：{}", pageNum, pageSize, storeId, BaseContext.getLoginUserId(), BaseContext.getLoginUserType());

        CustomerListQueryRequestDTO request = CustomerListQueryRequestDTO.builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .storeId(Long.valueOf(storeId))
                .build();

        CustomerListResponseDTO response = customerListAppService.getCustomerList(request);
        
        log.info("客户列表查询完成，返回{}条记录", 
                response.getList() != null ? response.getList().size() : 0);

        return response;
    }
}
