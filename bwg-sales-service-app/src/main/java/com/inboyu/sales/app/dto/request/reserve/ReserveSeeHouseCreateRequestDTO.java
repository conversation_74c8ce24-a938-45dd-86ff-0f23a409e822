package com.inboyu.sales.app.dto.request.reserve;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "预约看房创建请求")
public class ReserveSeeHouseCreateRequestDTO {

    @Schema(description = "客户ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long customerId;

    @Schema(description = "预约记录ID")
    private Long reserveSeeHouseId;

    @Schema(description = "员工ID")
    private Long staffId;

    @Schema(description = "门店ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "门店ID不能为空")
    private Long storeId;

    @Schema(description = "户型ID")
    private Long roomTypeId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "预约日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "预约日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate reserveDate;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime startTime;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime endTime;

    @Schema(description = "看房状态,字典维护,预约成功booked;已取消canceled;成功看房completed;逾期未看overdue")
    private String seeState;
}