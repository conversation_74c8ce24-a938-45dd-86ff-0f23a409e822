package com.inboyu.sales.app.controller;

import com.inboyu.sales.app.application.StoreSalesConfigAppService;
import com.inboyu.sales.app.dto.response.store.StoreSalesConfigDTO;
import com.inboyu.spring.cloud.starter.context.base.BaseContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/v1/customer-store-sales-config")
@Tag(name = "用户小程序-门店销售配置控制器")
public class CustomerStoreSalesConfigController {

    @Autowired
    private StoreSalesConfigAppService storeSalesConfigAppService;

    @GetMapping("/{storeId}")
    @Operation(summary = "用户小程序-根据门店ID查询销售配置")
    public ResponseEntity<StoreSalesConfigDTO> getStoreSalesConfigByStoreId(
            @PathVariable @Parameter(description = "门店ID") String storeId) {
        log.info("根据门店ID查询销售配置，门店ID: {}，用户ID：{}，用户类型：{}", storeId, BaseContext.getLoginUserId(), BaseContext.getLoginUserType());
        
        StoreSalesConfigDTO result = storeSalesConfigAppService.getStoreSalesConfigByStoreId(Long.valueOf(storeId));

        return ResponseEntity.ok(result);
    }
}
