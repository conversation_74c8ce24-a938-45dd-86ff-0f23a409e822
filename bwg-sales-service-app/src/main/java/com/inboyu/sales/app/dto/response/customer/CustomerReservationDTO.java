package com.inboyu.sales.app.dto.response.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 客户预约看房记录DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "客户预约看房记录")
public class CustomerReservationDTO {

    @Schema(description = "预约日期", example = "2025-09-08")
    private LocalDate reserveDate;

    @Schema(description = "开始时间", example = "09:00")
    private LocalTime startTime;

    @Schema(description = "结束时间", example = "10:00")
    private LocalTime endTime;

    @Schema(description = "约看状态", example = "成功预约")
    private String seeStateTitle;

    @Schema(description = "约看状态编码", example = "reserve_see_house_see_state.booked")
    private String seeState;

    @Schema(description = "格式化的时间显示", example = "09-08 09:00")
    private String formattedTime;

    @Schema(description = "格式化的状态显示", example = "09-08 09:00(成功预约)")
    private String formattedDisplay;
}
