package com.inboyu.sales.app.application.impl;

import com.inboyu.sales.app.application.ReserveSeeHouseAppService;
import com.inboyu.sales.app.dto.converter.ReserveSeeHouseAppConverter;
import com.inboyu.sales.app.dto.request.reserve.ReserveSeeHouseCreateRequestDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseCreateResponseDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseDetailResponseDTO;
import com.inboyu.sales.domain.customer.model.CustomerId;
import com.inboyu.sales.domain.customer.model.CustomerInfo;
import com.inboyu.sales.domain.customer.service.CustomerInfoService;
import com.inboyu.sales.domain.cycle.service.SalesCycleManagementDomainService;
import com.inboyu.sales.domain.reserve.model.ReserveSeeHouse;
import com.inboyu.sales.domain.reserve.repository.ReserveSeeHouseRepository;
import com.inboyu.sales.domain.store.model.Store;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.domain.store.model.StoreSalesConfig;
import com.inboyu.sales.domain.store.repository.StoreRepository;
import com.inboyu.sales.domain.store.repository.StoreSalesConfigRepository;
import com.inboyu.sales.exception.ResponseCode;
import com.inboyu.sales.infrastructure.constant.EventTypeEnum;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Log4j2
public class ReserveSeeHouseAppServiceImpl implements ReserveSeeHouseAppService {

    @Autowired
    private ReserveSeeHouseRepository reserveSeeHouseRepository;

    @Autowired
    private ReserveSeeHouseAppConverter reserveSeeHouseAppConverter;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private SalesCycleManagementDomainService salesCycleManagementDomainService;

    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private StoreSalesConfigRepository storeSalesConfigRepository;

    @Override
    public Pagination<ReserveSeeHouseDTO> pageReserveSeeHouse(Integer pageNum, Integer pageSize, Long customerId, Long storeId) {
        Pagination<ReserveSeeHouse> reserveSeeHouses = reserveSeeHouseRepository.pageReserveSeeHouse(pageNum, pageSize, customerId, storeId);

        List<String> storeIds = reserveSeeHouses.getList().stream()
                .map(reserveSeeHouse -> reserveSeeHouse.getStoreId() != null ? reserveSeeHouse.getStoreId().toString() : null)
                .filter(storeIdStr -> storeIdStr != null)
                .distinct()
                .toList();

        List<Store> stores = storeRepository.filterByStoreIds(storeIds.toArray(new String[0]));

        Map<Long, Store> storeMap = stores.stream()
                .collect(Collectors.toMap(Store::getStoreId, store -> store));

        List<ReserveSeeHouseDTO> storeDTOs = reserveSeeHouses.getList().stream()
                .map(reserveSeeHouse -> {
                    ReserveSeeHouseDTO dto = reserveSeeHouseAppConverter.toDTO(reserveSeeHouse);
                    if (dto.getStoreId() != null) {
                        Store store = storeMap.get(Long.valueOf(dto.getStoreId()));
                        if (store != null) {
                            dto.setStoreName(store.getStoreName());
                            dto.setStoreAddress(store.getStoreAddress());
                        }
                    }
                    return dto;
                }).toList();

        return new Pagination<>(
                reserveSeeHouses.getPageNum(),
                reserveSeeHouses.getPageSize(),
                reserveSeeHouses.getTotalPages(),
                reserveSeeHouses.getTotal(),
                storeDTOs
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReserveSeeHouseCreateResponseDTO createReserveSeeHouse(ReserveSeeHouseCreateRequestDTO dto) {
        log.info("创建预约看房记录: {}", dto);

        // 转换为领域模型
        ReserveSeeHouse newReservation = reserveSeeHouseAppConverter.toDomainFromCreateRequest(dto);

        // 安全地获取值对象的值，添加空值检查
        Long customerId = newReservation.getCustomerId() != null ? newReservation.getCustomerId().getValue() : null;
        Long storeId = newReservation.getStoreId() != null ? newReservation.getStoreId().getValue() : 0L;
        Long roomId = newReservation.getRoomId() != null ? newReservation.getRoomId().getValue() : 0L;
        Long roomTypeId = newReservation.getRoomTypeId() != null ? newReservation.getRoomTypeId().getValue() : 0L;

        // 获取门店销售配置
        StoreSalesConfig storeSalesConfig = storeSalesConfigRepository.findByStoreId(StoreId.of(storeId));

        // 校验门店销售配置
        validateStoreSalesConfig(storeSalesConfig, newReservation);

        if (customerId == null || storeId == 0L) {
            throw new AppException(ResponseCode.RESERVE_SEE_HOUSE_CUSTOMER_ID_OR_STORE_ID_NULL);
        }

        // 查询同一客户、同一门店、同一日期的现有预约记录
        ReserveSeeHouse existingReservation = reserveSeeHouseRepository.findLatestBookedReservation(
                customerId,
                storeId,
                roomId,
                roomTypeId,
                newReservation.getReserveDate()
        );

        ReserveSeeHouse saved;

        if (existingReservation == null) {
            // 1.1 没有现有预约，直接插入新记录
            saved = reserveSeeHouseRepository.save(newReservation);
        } else {
            // 1.2 存在现有预约，检查时间段冲突
            if (existingReservation.isRightTime(newReservation.getStartTime(), newReservation.getEndTime())){
                throw new AppException(ResponseCode.RESERVE_SEE_HOUSE_END_TIME_BEFORE_START);
            }

            if (existingReservation.isTimeConflict(newReservation.getStartTime(), newReservation.getEndTime())) {
                throw new AppException(ResponseCode.RESERVE_SEE_HOUSE_TIME_CONFLICT);
            } else {
                // 1.3 无时间段冲突，更新现有预约的时间
                existingReservation.updateTime(newReservation.getStartTime(), newReservation.getEndTime());
                saved = reserveSeeHouseRepository.save(existingReservation);
            }
        }

        // 第二到六步：执行销售周期管理
        salesCycleManagementDomainService.manageSalesCycle(
                saved.getCustomerId().getValue(),
                saved.getStoreId().getValue(),
                saved.getReserveSeeHouseId().getValue(),
                EventTypeEnum.RESERVE_SEE_HOUSE.getCode()
        );

        return reserveSeeHouseAppConverter.toCreateResponseDTO(saved);
    }

    @Override
    public ReserveSeeHouseDetailResponseDTO getReserveSeeHouseDetail(Long reserveSeeHouseId) {
        log.info("查询预约看房详情，ID：{}", reserveSeeHouseId);

        ReserveSeeHouse reserveSeeHouse = reserveSeeHouseRepository.findByReserveSeeHouseId(reserveSeeHouseId);

        if (reserveSeeHouse == null) {
            throw new AppException(ResponseCode.RESERVE_SEE_HOUSE_NOT_FOUND);
        }

        Store store = storeRepository.getStore(String.valueOf(reserveSeeHouse.getStoreId().getValue()));

        CustomerInfo customerInfo = customerInfoService.getCustomerInfo(CustomerId.of( reserveSeeHouse.getCustomerId().getValue()));

        if (customerInfo != null) {
            reserveSeeHouse.setCustomerInfo(customerInfo);
        }

        return reserveSeeHouseAppConverter.toDetailResponseDTO(reserveSeeHouse, store);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReserveSeeHouseDetailResponseDTO cancelReserveSeeHouse(Long reserveSeeHouseId) {

        // 1. 查询预约看房记录
        ReserveSeeHouse reserveSeeHouse = reserveSeeHouseRepository.findByReserveSeeHouseId(reserveSeeHouseId);
        if (reserveSeeHouse == null) {
            throw new AppException(ResponseCode.RESERVE_SEE_HOUSE_NOT_FOUND);
        }

        // 2. 检查是否可以取消
        if (!reserveSeeHouse.getStatus().canCancel()) {
            throw new AppException(ResponseCode.RESERVE_SEE_HOUSE_CANNOT_CANCEL,
                    reserveSeeHouse.getStatus().getDescription());
        }

        try {
            // 3. 使用聚合根的业务方法取消预约
            reserveSeeHouse.cancel();

            // 4. 保存状态变更
            ReserveSeeHouse updated = reserveSeeHouseRepository.save(reserveSeeHouse);

            log.info("预约看房取消成功，ID：{}，状态变更为：{}", reserveSeeHouseId, updated.getStatus().getDescription());

            // 5. 转换为响应DTO返回
            return reserveSeeHouseAppConverter.toDetailResponseDTO(updated);

        } catch (Exception e) {
            log.error("取消预约看房失败，ID：{}，错误信息：{}", reserveSeeHouseId, e.getMessage(), e);
            throw new AppException(ResponseCode.RESERVE_SEE_HOUSE_CANCEL_FAILED, e.getMessage());
        }
    }


    /**
     * 校验门店销售配置
     */
    private void validateStoreSalesConfig(StoreSalesConfig storeSalesConfig, ReserveSeeHouse reservation) {
        if (storeSalesConfig == null) {
            log.warn("该门店销售配置不存在");
            throw new AppException(ResponseCode.STORE_SALES_CONFIG_NOT_FOUND);
        }

        LocalDate reserveDate = reservation.getReserveDate();
        LocalTime startTime = reservation.getStartTime();
        LocalDate today = LocalDate.now();

        // 1. 校验是否开放预约看房
        if (!storeSalesConfig.getSeeHouseEnabled().isEnabled()) {
            log.warn("该门店已暂停预约了哦");
            throw new AppException(ResponseCode.STORE_SALES_CONFIG_ALREADY_PAUSED);

        }

        // 2. 校验预约开放日期
        LocalDate seeHouseDate = storeSalesConfig.getSeeHouseDate();
        if (seeHouseDate != null && reserveDate.isBefore(seeHouseDate)) {
            LocalDate endDate = seeHouseDate.plusDays(storeSalesConfig.getSeeHouseValidDay().getDays());
            log.warn("该项目暂未开放预约 该项目{} 至 {}开放预约", seeHouseDate, endDate);
            throw new AppException(
                    ResponseCode.STORE_SALES_CONFIG_NOT_OPENED.getCode(),
                    String.format("该项目暂未开放预约，该项目%s 至 %s开放预约", seeHouseDate, endDate)
            );
        }

        // 3. 校验可选星期
        if (!storeSalesConfig.getSeeHouseWeekDay().isAllowedDay(reserveDate.getDayOfWeek())) {
            log.info("选择的星期{}",storeSalesConfig.getSeeHouseWeekDay());
            log.warn("该项目{}未开放预约 请选择其他时间", reserveDate.getDayOfWeek());
            throw new AppException(
                    ResponseCode.STORE_SALES_CONFIG_NOT_OPENED.getCode(),
                    "该项目%s未开放预约，请选择其他时间".formatted(reserveDate.getDayOfWeek())
                    );
        }

        // 4. 校验预约时间范围
        LocalTime configStartTime = storeSalesConfig.getSeeHouseTimeStart();
        LocalTime configEndTime = storeSalesConfig.getSeeHouseTimeEnd();
        if (configStartTime != null && configEndTime != null) {
            if (startTime.isBefore(configStartTime) || startTime.isAfter(configEndTime)) {
                log.warn("该项目暂未开放预约 该项目{}至{}开放预约", configStartTime, configEndTime);
                throw new AppException(
                        ResponseCode.STORE_SALES_CONFIG_NOT_OPENED.getCode(),
                        String.format("该项目暂未开放预约，该项目%s 至 %s开放预约", configStartTime, configEndTime)
                );
            }
        }

        // 5. 校验是否开放当天预约
        if (reserveDate.equals(today) && !storeSalesConfig.getSeeHouseTodayEnabled().isEnabled()) {
            LocalDate startDate = today.plusDays(1);
            LocalDate endDate = startDate.plusDays(storeSalesConfig.getSeeHouseValidDay().getDays());
            log.warn("该项目暂未开放预约 该项目{}至{}开放预约", startDate, endDate);
            throw new AppException(
                    ResponseCode.STORE_SALES_CONFIG_NOT_OPENED.getCode(),
                    String.format("该项目暂未开放预约，该项目%s 至 %s开放预约", startDate, endDate)
            );
        }

        // 6. 校验可预约最大时间范围
        LocalDate earliestDate = storeSalesConfig.getSeeHouseTodayEnabled().isEnabled() ? today : today.plusDays(1);
        LocalDate maxValidDate = earliestDate.plusDays(storeSalesConfig.getSeeHouseValidDay().getDays());
        if (reserveDate.isAfter(maxValidDate)) {
            log.warn("该项目暂未开放预约 该项目{}至{}开放预约", earliestDate, maxValidDate);
            throw new AppException(
                    ResponseCode.STORE_SALES_CONFIG_NOT_OPENED.getCode(),
                    String.format("该项目暂未开放预约，该项目%s 至 %s开放预约", earliestDate, maxValidDate)
            );
        }
    }
}