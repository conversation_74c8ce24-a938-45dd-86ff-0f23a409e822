package com.inboyu.sales.exception;

import com.inboyu.spring.cloud.starter.common.constant.ResponseCodeInterface;

/**
 * <AUTHOR>
 * @date 2025年07月29日 20:04
 */
public enum ResponseCode implements ResponseCodeInterface {

    /** 通用响应码 */
    SUCCESS(0, "成功"),

    /** 销售服务错误码范围 16000 ～ 16999*/
    RESERVE_SEE_HOUSE_NOT_FOUND(16001, "预约看房记录不存在"),
    RESERVE_SEE_HOUSE_CANNOT_CANCEL(16002, "当前状态不允许取消预约"),
    RESERVE_SEE_HOUSE_ALREADY_CANCELED(16003, "预约已取消"),
    RESERVE_SEE_HOUSE_ALREADY_COMPLETED(16004, "预约已完成"),
    RESERVE_SEE_HOUSE_OVERDUE(16005, "预约已逾期"),
    RESERVE_SEE_HOUSE_TIME_CONFLICT(16006, "该时间段已有预约，请勿重复预约"),
    RESERVE_SEE_HOUSE_CANCEL_FAILED(16007, "取消预约看房失败"),
    RESERVE_SEE_HOUSE_END_TIME_BEFORE_START(16011, "结束时间不能早于开始时间"),
    PAGE_INFO_EXPIRED(16012, "页面信息已失效，请刷新后重试"),

    RESERVE_SEE_HOUSE_CUSTOMER_ID_OR_STORE_ID_NULL(16008, "客户ID和门店ID不能为空"),
    RESERVE_SEE_HOUSE_ROOM_TYPE_ID_AND_ROOM_ID_CANNOT_BOTH_BE_EMPTY(16009,"户型ID和房间ID不能同时为空"),
    RESERVE_SEE_HOUSE_STORE_ID_NULL(16010,"门店ID不能为空"),

    /** 门店销售配置相关错误码 */
    STORE_SALES_CONFIG_NOT_FOUND(16301, "门店销售配置不存在"),
    STORE_SALES_CONFIG_ALREADY_PAUSED(16302, "该门店已暂停预约了哦"),
    STORE_SALES_CONFIG_NOT_OPENED(16303, "该项目暂未开放预约"),

    /** 平台客户相关错误码 */
    PLATFORM_CUSTOMER_NOT_FOUND(16101, "平台客户不存在"),
    PLATFORM_CUSTOMER_ALREADY_EXISTS(16102, "平台客户已存在"),

    /** 客户信息相关错误码 */
    // 客户提醒保存异常
    CUSTOMER_REMINDER_SAVE_ERROR(16201, "客户提醒保存异常"),
    ;






    /** 销售服务错误码范围 16000 ～ 16999*/
    private final int code;
    private final String msg;

    ResponseCode(int code, String des) {
        this.code = code;
        this.msg = des;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }
}
