package com.inboyu.sales.domain.behavior.model;

import lombok.Value;

/**
 * <AUTHOR>
 * @date 2025/9/10
 */
@Value
public class BehaviorEventId {
    Long value;
    public BehaviorEventId(Long value) {
        if (null == value) {
            throw new IllegalArgumentException("客户行为事件id非法");
        }
        this.value = value;
    }

    public static BehaviorEventId of(Long value) {
        return new BehaviorEventId(value);
    }
}
