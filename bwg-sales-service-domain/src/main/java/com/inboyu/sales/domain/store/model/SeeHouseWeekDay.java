package com.inboyu.sales.domain.store.model;

import lombok.Builder;
import lombok.Value;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/9/3
 */
@Value
@Builder
public class SeeHouseWeekDay {
    /**
     * 编码
     */
    String code;
    /**
     * 值
     */
    String value;

    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_MONDAY = "store_sales_config_see_house_week_day.Monday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_TUESDAY = "store_sales_config_see_house_week_day.Tuesday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_WEDNESDAY = "store_sales_config_see_house_week_day.Wednesday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_THURSDAY = "store_sales_config_see_house_week_day.Thursday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_FRIDAY = "store_sales_config_see_house_week_day.Friday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_SATURDAY = "store_sales_config_see_house_week_day.Saturday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_SUNDAY = "store_sales_config_see_house_week_day.Sunday";


    public boolean isAllowedDay(java.time.DayOfWeek dayOfWeek) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }

        String targetCode = switch (dayOfWeek) {
            case MONDAY -> STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_MONDAY;
            case TUESDAY -> STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_TUESDAY;
            case WEDNESDAY -> STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_WEDNESDAY;
            case THURSDAY -> STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_THURSDAY;
            case FRIDAY -> STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_FRIDAY;
            case SATURDAY -> STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_SATURDAY;
            case SUNDAY -> STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_SUNDAY;
        };

        return Arrays.stream(code.split(","))
                .map(String::trim)
                .anyMatch(targetCode::equals);
    }

    public static int codeToDayOfWeek(String code) {
        return switch (code) {
            case STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_MONDAY -> 1;
            case STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_TUESDAY -> 2;
            case STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_WEDNESDAY -> 3;
            case STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_THURSDAY -> 4;
            case STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_FRIDAY -> 5;
            case STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_SATURDAY -> 6;
            case STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_SUNDAY -> 7;
            default -> 0;
        };
    }
}
