package com.inboyu.sales.domain.customer.repository;

import com.inboyu.sales.domain.customer.model.PlatformCustomer;

public interface PlatformCustomerRepository {
    /**
     * 根据客户ID查询平台客户
     * @param customerId 客户ID
     * @return 平台客户，不存在返回null
     */
    PlatformCustomer findByCustomerId(Long customerId);

    /**
     * 保存平台客户
     * @param platformCustomer 平台客户
     * @return 保存后的平台客户
     */
    PlatformCustomer save(PlatformCustomer platformCustomer);
}