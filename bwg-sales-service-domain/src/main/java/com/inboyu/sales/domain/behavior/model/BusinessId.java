package com.inboyu.sales.domain.behavior.model;

import lombok.Value;

/**
 * <AUTHOR>
 * @date 2025/9/10
 */
@Value
public class BusinessId {
    Long value;
    public BusinessId(Long value) {
        if (null == value) {
            throw new IllegalArgumentException("业务id非法");
        }
        this.value = value;
    }

    public static BusinessId of(Long value) {
        return new BusinessId(value);
    }
}
