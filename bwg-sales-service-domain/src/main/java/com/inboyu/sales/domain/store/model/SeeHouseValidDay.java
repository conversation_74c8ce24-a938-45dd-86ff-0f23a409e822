package com.inboyu.sales.domain.store.model;

import lombok.Builder;
import lombok.Value;

/**
 * <AUTHOR>
 * @date 2025/9/3
 */
@Value
@Builder
public class SeeHouseValidDay {
    /**
     * 编码
     */
    String code;
    /**
     * 值
     */
    String value;

    public final static String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_7 = "store_sales_config_see_house_valid_day.7";
    public final static String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_14 = "store_sales_config_see_house_valid_day.14";
    public final static String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_30 = "store_sales_config_see_house_valid_day.30";

    //添加 getDays
    public int getDays() {
        // 添加防护机制，避免异常配置导致OOM
        if (this.code == null || this.code.trim().isEmpty()) {
            throw new IllegalArgumentException("看房有效天数编码不能为空");
        }

        int days = switch (this.code) {
            case STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_7 -> 7;
            case STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_14 -> 14;
            case STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_30 -> 30;
            default -> {
                // 尝试从编码中解析数字，但限制最大值防止OOM
                try {
                    String numberPart = this.code.substring(this.code.lastIndexOf('.') + 1);
                    int parsedDays = Integer.parseInt(numberPart);
                    // 限制最大天数为90天，防止内存溢出
                    if (parsedDays <= 0) {
                        throw new IllegalArgumentException("看房有效天数必须大于0: " + this.code);
                    }
                    if (parsedDays > 90) {
                        throw new IllegalArgumentException("看房有效天数不能超过90天，当前配置: " + parsedDays + "天");
                    }
                    yield parsedDays;
                } catch (NumberFormatException | StringIndexOutOfBoundsException e) {
                    throw new IllegalArgumentException("无效的看房有效天数编码: " + this.code, e);
                }
            }
        };

        return days;
    }
}
