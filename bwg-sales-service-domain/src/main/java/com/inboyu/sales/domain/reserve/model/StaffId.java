package com.inboyu.sales.domain.reserve.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 员工ID值对象
 */
@Getter
@EqualsAndHashCode
public class StaffId {

    private final Long value;

    private StaffId(Long value) {
        this.value = value;
    }

    public static StaffId of(Long value) {
        return new StaffId(value);
    }

    @Override
    public String toString() {
        return value != null && value == 0L ? "" : String.valueOf(value);
    }
}
