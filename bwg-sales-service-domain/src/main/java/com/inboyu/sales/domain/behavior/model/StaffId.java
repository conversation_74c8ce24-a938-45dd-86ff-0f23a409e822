package com.inboyu.sales.domain.behavior.model;

import lombok.Value;

/**
 * <AUTHOR>
 * @date 2025/9/10
 */
@Value
public class StaffId {
    Long value;
    public StaffId(Long value) {
        if (null == value) {
            throw new IllegalArgumentException("员工id非法");
        }
        this.value = value;
    }

    public static StaffId of(Long value) {
        return new StaffId(value);
    }
}
