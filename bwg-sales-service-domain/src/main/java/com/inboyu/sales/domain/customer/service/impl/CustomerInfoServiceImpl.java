package com.inboyu.sales.domain.customer.service.impl;

import com.inboyu.sales.domain.customer.model.CustomerId;
import com.inboyu.sales.domain.customer.model.CustomerInfo;
import com.inboyu.sales.domain.customer.repository.CustomerInfoRepository;
import com.inboyu.sales.domain.customer.service.CustomerInfoService;
import com.inboyu.sales.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客户信息领域服务实现
 */
@Service
public class CustomerInfoServiceImpl implements CustomerInfoService {

    @Autowired
    private CustomerInfoRepository customerInfoRepository;

    @Override
    public CustomerInfo getCustomerInfo(CustomerId customerId) {

        // 业务规则：参数校验
        if (customerId == null) {
            throw new IllegalArgumentException("客户ID不能为空");
        }

        var customerInfo = customerInfoRepository.getCustomerInfo(String.valueOf(customerId.getValue()));
        if (customerInfo == null) {
            throw new AppException(ResponseCode.PLATFORM_CUSTOMER_NOT_FOUND);
        }
        return customerInfo;
    }
}
