package com.inboyu.sales.domain.customer.model;

import lombok.Value;

/**
 * <AUTHOR>
 * @date 2025/9/10
 */
@Value
public class CustomerId {
    Long value;
    public CustomerId(Long value) {
        if (null == value) {
            throw new IllegalArgumentException("客户id非法");
        }
        this.value = value;
    }

    public static CustomerId of(Long value) {
        return new CustomerId(value);
    }
}
