package com.inboyu.sales.domain.behavior.model;

import com.inboyu.sales.domain.customer.model.CustomerId;
import com.inboyu.sales.domain.reserve.model.StaffId;
import com.inboyu.sales.domain.store.model.StoreId;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * 行为事件聚合根
 */
@Getter
@Setter
@Builder
public class BehaviorEvent {

    /**
     * 行为事件业务ID
     */
    private BehaviorEventId behaviorEventId;

    /**
     * 行为事件类型
     */
    private EnventType eventType;

    /**
     * 事件内容
     */
    private String eventContent;

    /**
     * 对应业务表的id
     */
    private BusinessId businessId;

    /**
     * 客户ID
     */
    private CustomerId customerId;

    /**
     * 门店ID
     */
    private StoreId storeId;

    /**
     * 员工ID
     */
    private StaffId staffId;

    public boolean isHasBehaviorEventId() {
        return behaviorEventId != null && behaviorEventId.getValue() != null;
    }
}
