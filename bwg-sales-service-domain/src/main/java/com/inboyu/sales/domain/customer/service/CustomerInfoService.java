package com.inboyu.sales.domain.customer.service;

import com.inboyu.sales.domain.customer.model.CustomerId;
import com.inboyu.sales.domain.customer.model.CustomerInfo;
import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;

/**
 * 客户信息领域服务接口
 */
public interface CustomerInfoService {

    /**
     * 获取客户信息
     */
    CustomerInfo getCustomerInfo(CustomerId customerId);
}
