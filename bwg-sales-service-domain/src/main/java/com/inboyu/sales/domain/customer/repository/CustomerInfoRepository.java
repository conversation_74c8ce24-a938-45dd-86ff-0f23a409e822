package com.inboyu.sales.domain.customer.repository;

import com.inboyu.sales.domain.customer.model.CustomerId;
import com.inboyu.sales.domain.customer.model.CustomerInfo;
import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import org.springframework.stereotype.Repository;

/**
 * 客户信息仓储接口
 * 只负责数据访问，不包含业务逻辑
 */
@Repository
public interface CustomerInfoRepository {

    /**
     * 获取客户信息
     * Repository层只负责基础数据查询，业务逻辑由Domain Service处理
     *
     * @param customerId  客户ID（必填）
     * @return 客户信息
     */
    CustomerInfo getCustomerInfo(String customerId);
}
